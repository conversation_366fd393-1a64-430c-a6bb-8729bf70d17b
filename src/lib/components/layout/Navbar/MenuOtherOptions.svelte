<script lang="ts">
	import { getContext, createEventDispatcher } from 'svelte';
	import { flyAndScale } from '$lib/utils/transitions';
	import { DropdownMenu } from 'bits-ui';
	import Dropdown from '$lib/components/common/Dropdown.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import Keyboard from '$lib/components/icons/Keyboard.svelte';
	import ArchiveBox from '$lib/components/icons/ArchiveBox.svelte';
	import { showArchivedChats } from '$lib/stores';
	import ShortcutsModal from '$lib/components/chat/ShortcutsModal.svelte';
	import { goto } from '$app/navigation';
	import Cog6 from '$lib/components/icons/Cog6.svelte';
	import { user } from '$lib/stores';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	let showShortcuts = false;
</script>

<Dropdown
	on:change={(e) => {
		if (e.detail === false) {
			dispatch('close');
		}
	}}
>
	<Tooltip content={$i18n.t('More')}>
		<slot />
	</Tooltip>

	<div slot="content">
		<DropdownMenu.Content
			class="w-full max-w-[200px] rounded-xl px-1 py-1.5 z-50 bg-white dark:bg-gray-850 dark:text-white shadow-lg"
			sideOffset={-2}
			side="bottom"
			align="start"
			transition={flyAndScale}
		>
			<!-- Keyboard shortcuts -->
			<DropdownMenu.Item
				class="flex gap-2 items-center px-3 py-2 text-sm dark:hover:bg-gray-800 only-icon-button"
				on:click={() => {
					showShortcuts = !showShortcuts;
				}}
			>
				<Keyboard className="size-5 only-icon-button-fill-svg" />
				<div class="flex items-center">{$i18n.t('Keyboard shortcuts')}</div>
			</DropdownMenu.Item>
			<!-- Archive -->
			<DropdownMenu.Item
				class="flex gap-2 items-center px-3 py-2 text-sm dark:hover:bg-gray-800 only-icon-button"
				on:click={() => {
					showArchivedChats.set(true);
				}}
			>
				<ArchiveBox className="size-5 only-icon-button-stroke-svg" strokeWidth="2" />
				<div class="flex items-center">{$i18n.t('Archive')}</div>
			</DropdownMenu.Item>
			<!-- Admin settings-->
			{#if $user?.role === 'admin'}
				<DropdownMenu.Item
					class="flex gap-2 items-center px-3 py-2 text-sm dark:hover:bg-gray-800 only-icon-button"
					on:click={() => {
						goto('/admin/settings');
					}}
				>
					<Cog6 className="size-5 only-icon-button-stroke-svg" strokeWidth="2" />
					<div class="flex items-center">{$i18n.t('Admin Panel')}</div>
				</DropdownMenu.Item>
			{/if}
		</DropdownMenu.Content>
	</div>
</Dropdown>

<ShortcutsModal bind:show={showShortcuts} />
