<script lang="ts">
	import { onMount, tick } from 'svelte';

	export let id = '';
	export let value = '';
	export let placeholder = '';
	export let className =
		'w-full rounded-lg px-3 py-2 text-sm bg-gray-50 dark:text-gray-300 dark:bg-gray-850 outline-none resize-none';
	export let autoResize = false;
	export let maxHeight = 320;
	export let minHeight = 40;
	export let rows = undefined;
	export let onKeydown: Function = () => {};

	let textareaElement: HTMLTextAreaElement;

	//if autoResize is set to true, the height should grow with content value, but never exceed the maxHeight, and go below minHeight.
	const adjustHeight = () => {
		if (textareaElement && autoResize) {
			textareaElement.style.height = 'auto';
			const newHeight = Math.max(minHeight, Math.min(textareaElement.scrollHeight, maxHeight));
			textareaElement.style.height = `${newHeight}px`;
		}
	};

	onMount(async () => {
		await tick();
		if (autoResize) {
			adjustHeight();
		}
	});

	// Handle paste event to ensure only plaintext is pasted
	function handlePaste(event: ClipboardEvent) {
		event.preventDefault(); // Prevent the default paste action
		const clipboardData = event.clipboardData?.getData('text/plain'); // Get plaintext from clipboard
		// Insert plaintext into the textarea
		document.execCommand('insertText', false, clipboardData);
		if (autoResize) {
			tick().then(() => adjustHeight());
		}
	}

	function handleInput() {
		const text = textareaElement.value;
		if (text === '\n') {
			textareaElement.value = '';
			value = '';
			return;
		}
		value = text;

		if (autoResize) {
			tick().then(() => adjustHeight());
		}
	}
</script>

<textarea
	{id}
	{rows}
	bind:this={textareaElement}
	bind:value
	class="{className} whitespace-pre-wrap relative {value?.trim() ? '' : 'placeholder'}"
	style="{autoResize
		? `min-height: ${minHeight}px; max-height: ${maxHeight}px;`
		: 'field-sizing: content;'} -moz-user-select: text !important;"
	on:input={handleInput}
	on:focus={() => {
		if (autoResize) {
			tick().then(() => adjustHeight());
		}
	}}
	on:paste={handlePaste}
	on:keydown={onKeydown}
	data-placeholder={placeholder}
/>

<style>
	.placeholder::before {
		position: absolute;
		content: attr(data-placeholder);
		color: #adb5bd;
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		pointer-events: none;
		touch-action: none;
	}
</style>
