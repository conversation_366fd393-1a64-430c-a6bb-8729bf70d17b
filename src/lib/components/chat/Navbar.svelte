<script lang="ts">
	import { getContext, tick } from 'svelte';
	import { config } from '$lib/stores';

	import { WEBUI_NAME, chatId, mobile, showControls, showSidebar } from '$lib/stores';

	import ShareChatModal from '../chat/ShareChatModal.svelte';
	import ModelSelector from '../chat/ModelSelector.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import AdjustmentsHorizontal from '../icons/AdjustmentsHorizontal.svelte';
	import NewChatIcon from '../icons/NewChatIcon.svelte';
	import EllipsisHorizontal from '../icons/EllipsisHorizontal.svelte';
	import MenuDrawerClosed from '../icons/MenuDrawerClosed.svelte';
	import MenuOtherOptions from '../layout/Navbar/MenuOtherOptions.svelte';

	const i18n = getContext('i18n');

	export let initNewChat: Function;
	export let title: string = $WEBUI_NAME;
	export let shareEnabled: boolean = false;

	export let chat;
	export let selectedModels;
	export let showModelSelector = true;

	let showShareChatModal = false;

	async function focusFirstChatItem() {
		// Wait for the DOM to update before focusing the first chat item
		await tick();

		const firstChatItem = document.getElementById('sidebar-open-toggle-button');
		if (firstChatItem) {
			firstChatItem.focus();
		}
	}
</script>

<ShareChatModal bind:show={showShareChatModal} chatId={$chatId} />

<div class="chat-console-header">
	<div
		class=" bg-gradient-to-b via-50% from-white via-white to-transparent dark:from-gray-900 dark:via-gray-900 dark:to-transparent pointer-events-none absolute inset-0 -bottom-7 z-[-1] blur"
	></div>

	<div class=" flex max-w-full w-full mx-auto px-1 pt-0.5 bg-transparent">
		<div class="flex items-center w-full max-w-full">
			<div
				class="flex-1 overflow-hidden max-w-full py-0.5
				{$showSidebar ? 'ml-1' : ''}
				"
			>
				{#if showModelSelector}
					<ModelSelector bind:selectedModels showSetDefault={!shareEnabled} />
				{/if}
			</div>

			<div class="self-start flex flex-none items-center text-steel-800 dark:text-gray-400">
				<!-- <div class="md:hidden flex self-center w-[1px] h-5 mx-2 bg-gray-300 dark:bg-stone-700" /> -->
				{#if $mobile && $config?.features?.enable_chat_controls}
					<Tooltip content={$i18n.t('Controls')}>
						<button
							class=" flex cursor-pointer px-1.5 py-1.5 dark:hover:bg-gray-850 transition only-icon-button"
							on:click={async () => {
								await showControls.set(!$showControls);
							}}
							aria-label="Controls"
						>
							<div class=" m-auto self-center">
								<AdjustmentsHorizontal
									class="only-icon-button-stroke-svg only-icon-button-fill-svg"
									className=" size-5"
									strokeWidth="0.5"
								/>
							</div>
						</button>
					</Tooltip>
				{/if}

				{#if !$mobile && $config?.features?.enable_chat_controls}
					<Tooltip content={$i18n.t('Controls')}>
						<button
							class=" flex cursor-pointer px-1.5 py-1.5 dark:hover:bg-gray-850 transition only-icon-button"
							on:click={async () => {
								await showControls.set(!$showControls);
							}}
							aria-label="Controls"
						>
							<div class=" m-auto self-center">
								<AdjustmentsHorizontal
									className="only-icon-button-stroke-svg only-icon-button-fill-svg size-5"
									strokeWidth="0.5"
								/>
							</div>
						</button>
					</Tooltip>
				{/if}
				<MenuOtherOptions>
					<button
						class="flex cursor-pointer px-2 py-2 dark:hover:bg-gray-850 transition only-icon-button"
						id="chat-menu-other-options-button"
					>
						<div class=" m-auto self-center">
							<!-- Elipsis icon -->
							<EllipsisHorizontal className="size-5  only-icon-button-fill-svg" />
						</div>
					</button>
				</MenuOtherOptions>
			</div>
		</div>
	</div>
</div>
