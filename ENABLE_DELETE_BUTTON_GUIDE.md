# How to Enable Chat Delete Button

## Quick Setup

To enable the delete button functionality, you need to set the `ENABLE_DELETE_BUTTON` configuration to `true`.

## Method 1: Environment Variable

Set the environment variable before starting the application:

```bash
export ENABLE_DELETE_BUTTON=true
```

Then restart your Open WebUI application.

## Method 2: Docker Environment

If you're using Docker, add the environment variable to your docker run command:

```bash
docker run -d \
  -p 3000:8080 \
  -e ENABLE_DELETE_BUTTON=true \
  -v open-webui:/app/backend/data \
  --name open-webui \
  ghcr.io/open-webui/open-webui:main
```

Or in your `docker-compose.yml`:

```yaml
version: '3.8'
services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "3000:8080"
    environment:
      - ENABLE_DELETE_BUTTON=true
    volumes:
      - open-webui:/app/backend/data
```

## Method 3: Configuration File

If you're using a configuration file, update the `ENABLE_DELETE_BUTTON` setting:

```python
ENABLE_DELETE_BUTTON = True
```

## Verification

After enabling the delete button:

1. Restart your Open WebUI application
2. Navigate to any chat
3. Click on the chat menu (three dots or hamburger menu)
4. You should now see a "Delete" option in the menu

## Enhanced Delete Features

With the enhanced delete functionality, when you delete a chat:

✅ **Chat record** is removed from the database  
✅ **Associated tags** are cleaned up  
✅ **Uploaded documents** are removed from vector database  
✅ **Web search data** is removed from vector database  
✅ **File collections** are properly cleaned up  

## Permissions

The delete functionality respects user permissions:

- **Admin users**: Can delete any chat
- **Regular users**: Can only delete their own chats (if they have `chat.delete` permission)
- **Permission-restricted users**: Cannot delete chats if `chat.delete` permission is not granted

## Safety Features

- **Confirmation dialogs**: UI will typically show confirmation before deletion
- **Error handling**: Vector database cleanup errors won't block chat deletion
- **Logging**: All delete operations are logged for audit purposes
- **Graceful degradation**: System continues to work even if some cleanup fails

## Troubleshooting

### Delete Button Not Visible

1. Verify `ENABLE_DELETE_BUTTON=true` is set
2. Restart the application completely
3. Clear browser cache and refresh
4. Check browser console for any JavaScript errors

### Delete Operation Fails

1. Check application logs for error messages
2. Verify user has proper permissions
3. Ensure vector database is accessible
4. Check that the chat exists and belongs to the user

### Partial Cleanup

If vector database cleanup partially fails:
- The chat will still be deleted from the main database
- Check logs for specific collection deletion errors
- Orphaned vector data won't affect functionality but may consume storage

## Monitoring

To monitor delete operations, check logs for these messages:

- `INFO`: "Deleted vector collection: {collection_name}"
- `INFO`: "Successfully cleaned up {count} vector collections for chat {chat_id}"
- `WARNING`: "Failed to delete vector collection {collection_name}: {error}"
- `ERROR`: "Error cleaning up vector data for chat {chat_id}: {error}"

## Security Considerations

- Users can only delete their own chats (unless they're admin)
- Deleted data is permanently removed from vector database
- File cleanup ensures no orphaned document data remains
- All operations are logged for audit trails

## Performance Impact

The enhanced delete functionality:
- Adds minimal overhead to delete operations
- Improves long-term system performance by preventing data accumulation
- Reduces vector database size and search times
- Operates asynchronously to avoid blocking the UI
