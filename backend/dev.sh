#!/usr/bin/env bash

# turn Datadog off for local dev
export ENABLE_TRACING=false
export ENABLE_PROFILING=false
export DD_INSTRUMENTATION_TELEMETRY_ENABLED=false
export DD_TELEMETRY_ENABLED=false
export DD_TRACE_ENABLED=false

SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
cd "$SCRIPT_DIR" || exit

PORT="${PORT:-8080}"
lsof -ti:$PORT | xargs kill -9
uvicorn open_webui.main:app --port $PORT --host 0.0.0.0 --forwarded-allow-ips '*' --reload
