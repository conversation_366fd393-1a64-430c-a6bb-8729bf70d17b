#!/usr/bin/env python3
"""
Test script for enhanced chat delete functionality.
This script demonstrates how the enhanced delete works with mock data.
"""

import json
import logging
from unittest.mock import Mock, patch

# Mock the vector database client
class MockVectorDBClient:
    def __init__(self):
        self.deleted_collections = []
    
    def delete_collection(self, collection_name: str):
        self.deleted_collections.append(collection_name)
        print(f"Mock: Deleted collection {collection_name}")

# Mock the Files model
class MockFiles:
    @staticmethod
    def get_file_by_id(file_id: str):
        # Mock file record with metadata
        mock_file = Mock()
        mock_file.meta = {"collection_name": f"file-{file_id}"}
        return mock_file

def test_cleanup_chat_vector_data():
    """Test the cleanup function with various chat data scenarios."""
    
    # Import the function (in real usage, this would be from the actual module)
    # For testing, we'll define it inline
    def cleanup_chat_vector_data(chat_id: str, chat_data: dict, vector_client, files_model) -> None:
        """Test version of the cleanup function."""
        try:
            files = chat_data.get("files", [])
            collections_to_delete = set()
            file_ids_to_check = set()
            
            for file_item in files:
                if isinstance(file_item, dict):
                    if file_item.get("type") == "file" and file_item.get("id"):
                        file_id = file_item["id"]
                        collection_name = f"file-{file_id}"
                        collections_to_delete.add(collection_name)
                        file_ids_to_check.add(file_id)
                    
                    if file_item.get("collection_name"):
                        collection_name = file_item["collection_name"]
                        if (collection_name.startswith("web-search-") or 
                            collection_name.startswith("websearch-") or
                            collection_name.startswith(f"chat-{chat_id}-")):
                            collections_to_delete.add(collection_name)
            
            # Check file metadata
            for file_id in file_ids_to_check:
                try:
                    file_record = files_model.get_file_by_id(file_id)
                    if file_record and file_record.meta and file_record.meta.get("collection_name"):
                        additional_collection = file_record.meta["collection_name"]
                        collections_to_delete.add(additional_collection)
                except Exception:
                    pass
            
            # Delete collections
            for collection_name in collections_to_delete:
                try:
                    vector_client.delete_collection(collection_name=collection_name)
                except Exception as e:
                    print(f"Failed to delete {collection_name}: {e}")
            
            print(f"Cleaned up {len(collections_to_delete)} collections for chat {chat_id}")
            
        except Exception as e:
            print(f"Error cleaning up vector data for chat {chat_id}: {e}")

    # Test scenarios
    print("=== Testing Enhanced Chat Delete Functionality ===\n")
    
    # Test 1: Chat with uploaded files
    print("Test 1: Chat with uploaded files")
    mock_vector_client = MockVectorDBClient()
    mock_files = MockFiles()
    
    chat_data_with_files = {
        "files": [
            {
                "type": "file",
                "id": "file123",
                "name": "document.pdf",
                "collection_name": "file-file123"
            },
            {
                "type": "file", 
                "id": "file456",
                "name": "spreadsheet.xlsx",
                "collection_name": "file-file456"
            }
        ]
    }
    
    cleanup_chat_vector_data("chat001", chat_data_with_files, mock_vector_client, mock_files)
    print(f"Deleted collections: {mock_vector_client.deleted_collections}\n")
    
    # Test 2: Chat with web search
    print("Test 2: Chat with web search")
    mock_vector_client = MockVectorDBClient()
    
    chat_data_with_websearch = {
        "files": [
            {
                "type": "doc",
                "name": "Web Search Results",
                "collection_name": "web-search-abc123def456"
            },
            {
                "type": "doc",
                "name": "Another Search",
                "collection_name": "web-search-xyz789"
            }
        ]
    }
    
    cleanup_chat_vector_data("chat002", chat_data_with_websearch, mock_vector_client, mock_files)
    print(f"Deleted collections: {mock_vector_client.deleted_collections}\n")
    
    # Test 3: Mixed chat with files and web search
    print("Test 3: Mixed chat with files and web search")
    mock_vector_client = MockVectorDBClient()
    
    chat_data_mixed = {
        "files": [
            {
                "type": "file",
                "id": "file789",
                "name": "report.docx",
                "collection_name": "file-file789"
            },
            {
                "type": "doc",
                "name": "Research Results",
                "collection_name": "web-search-research123"
            },
            {
                "type": "collection",
                "name": "Custom Collection",
                "collection_name": "chat-chat003-custom"
            }
        ]
    }
    
    cleanup_chat_vector_data("chat003", chat_data_mixed, mock_vector_client, mock_files)
    print(f"Deleted collections: {mock_vector_client.deleted_collections}\n")
    
    # Test 4: Empty chat
    print("Test 4: Empty chat (no files)")
    mock_vector_client = MockVectorDBClient()
    
    chat_data_empty = {"files": []}
    
    cleanup_chat_vector_data("chat004", chat_data_empty, mock_vector_client, mock_files)
    print(f"Deleted collections: {mock_vector_client.deleted_collections}\n")
    
    print("=== All tests completed ===")

if __name__ == "__main__":
    test_cleanup_chat_vector_data()
